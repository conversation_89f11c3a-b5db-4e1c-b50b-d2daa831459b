
<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { reactive, onBeforeUnmount } from 'vue';
  import { getSpeakersList } from '@/api/avatarChat';
  import { deleteVoice, renameVoice, StartCloning, getVoiceList } from '@/api/avatarChat';
  import { getLocalItem } from '@/utils';
  import { Modal, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/index.vue';
  import Loading from '@/components/Loading/index.vue';
  import AddModal from '@/views/AvatarChat/Sound/AddModal/index.vue';

  import stopIcon from '@/assets/image/base/pictures/stop.png';
  import playIcon from '@/assets/image/base/pictures/play.png';
  import EmptyImage from '@/assets/image/base/pictures/emptyImage.png';
  import videoIcon from '@/assets/image/base/pictures/video.png';

  // 获取用户ID
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  const speakersData = ref<Record<string, any[]>>({}); // 说话人音色列表
  const tagsData = reactive(['普通话', '方言', '外语', '我的声音']); // 标签列表
  // 映射标签到数据键
  const tagToKeyMap = {
    普通话: 'mandarin',
    方言: 'dialect',
    外语: 'foreign',
    我的声音: 'myVoice',
  };

  // 当前选中的语言类型
  const selectedLanguage = ref('mandarin');
  const selectedSpeaker = ref<string | null>(null);
  const hoverStates = reactive<Record<number, boolean>>({});
  const selectedTag = ref<string | null>('普通话');
  const isPlaying = reactive<Record<number, boolean>>({});
  const isOpenModal = ref(false);
  const myVoiceHoverIndex = ref<number | null>(null);

  // 打开创建声音的弹窗
  const showAddModal = ref<boolean>(false);

  // 重命名相关变量
  const openNameModal = ref(false);
  const personName = ref('');
  const nameInputError = ref(false);
  const currentRenamingItem = ref<any>(null);

  // 定义 props 和 emits
  const props = defineProps<{
    handleCloseModal?: () => void;
  }>();

  const emit = defineEmits<{
    'speaker-selected': [speaker: { name: string; description: string }];
  }>();

  const voiceList = ref<any[]>([]);
  const voiceListLoading = ref<boolean>(false);

  const openSpeakersModal = () => {
    isOpenModal.value = true;
    // trainMode.value = 'fast';
    // uploadAvatarUrl.value = '';
    // preViewUrl.value = '';
    // selectedDigitalMan.value = { index: 0, url: '', gender: '' };
    // isNameEntered.value = true;
    // isAvatarUploaded.value = true;
    // queryDigitalHumanList();
  };

  defineExpose({
    openSpeakersModal,
  });

  const handleOk = () => {
    // 如果还没有选中声音，可以选择第一个
    if (!selectedSpeaker.value) {
      const currentSpeakers = speakersData.value[selectedLanguage.value] || [];
      if (currentSpeakers.length > 0) {
        handleSpeakerSelect(currentSpeakers[0].name);
      }
    }
    isOpenModal.value = false;
  };

  const handleCancel = () => {
    isOpenModal.value = false;
  };

  const getVoiceListData = async () => {
    voiceListLoading.value = true;
    try {
      const data = await getVoiceList(userId);
      console.log('获取声音列表成功:', data);
      voiceList.value = data || [];

      // 更新 speakersData.myVoice
      speakersData.value = {
        ...speakersData.value,
        myVoice: voiceList.value
      };
    } catch (error) {
      console.error('获取声音列表失败:', error);
      voiceList.value = [];
      // 确保 myVoice 为空数组
      speakersData.value = {
        ...speakersData.value,
        myVoice: []
      };
    } finally {
      voiceListLoading.value = false;
    }
  };

  // 说话人音色数据
  const getSpeakersData = async () => {
    try {
      const data = await getSpeakersList();
      speakersData.value = {...data, myVoice: voiceList.value}; // 设置音色数据
    } catch (error) {
      console.error('获取音色列表失败:', error);
    }
  };

  // 页面加载时调用
  onMounted(async () => {
    // 先获取我的声音数据
    await getVoiceListData();
    // 再获取其他音色数据
    await getSpeakersData();
  });

  // 管理音频元素
  const audioElements = reactive<Record<number, HTMLAudioElement | null>>({});

  const handleChange = (tag: string) => {
    selectedTag.value = tag; // 设置当前选中的标签
    // 获取对应的数据键
    const key = tagToKeyMap[tag as keyof typeof tagToKeyMap];
    if (key) {
      selectedLanguage.value = key;

      // 清空所有播放状态和音频元素
      Object.keys(isPlaying).forEach((index) => {
        isPlaying[Number(index)] = false;
      });
      Object.values(audioElements).forEach((audio) => {
        audio?.pause();
        audio?.remove();
      });
      Object.keys(audioElements).forEach((index) => {
        audioElements[Number(index)] = null;
      });
    } else {
      console.warn(`未找到与标签 "${tag}" 对应的语言类型`);
    }
  };

  // 播放音频
  const playAudio = (index: number, sampleFileUrl: string) => {
    // 先停止所有正在播放的音频
    Object.keys(isPlaying).forEach((key) => {
      const keyIndex = Number(key);
      if (isPlaying[keyIndex] && keyIndex !== index) {
        isPlaying[keyIndex] = false;
        if (audioElements[keyIndex]) {
          audioElements[keyIndex]?.pause();
        }
      }
    });

    if (!audioElements[index]) {
      audioElements[index] = new Audio(sampleFileUrl);
      // 添加 ended 事件监听器
      audioElements[index]!.addEventListener('ended', () => {
        isPlaying[index] = false; // 停止播放状态
      });
    }

    const audio = audioElements[index];
    if (audio) {
      if (audio.paused) {
        audio
          .play()
          .then(() => {
            isPlaying[index] = true;
          })
          .catch((err) => {
            console.error('音频播放失败:', err);
          });
      } else {
        audio.pause();
        isPlaying[index] = false;
      }
    }
  };

  // 选择说话人
  const handleSpeakerSelect = (speakerName: string) => {
    selectedSpeaker.value = speakerName;
    // 其余逻辑保持不变
    const currentSpeakers = speakersData.value[selectedLanguage.value] || [];
    const selectedSpeakerData = currentSpeakers.find((speaker) => speaker.name === speakerName);

    if (selectedSpeakerData) {
      // 按照指定格式回显：普通话-灵动女声
      // 如果不存在description，则使用name字段
      const displayName = selectedSpeakerData.description || selectedSpeakerData.name;
      const displayText = `${selectedTag.value}-${displayName}`;

      emit('speaker-selected', {
        name: selectedSpeakerData.name,
        description: displayText,
      });

      // 立即关闭弹窗
      isOpenModal.value = false;
    }
  };
  // 组件卸载时销毁音频元素
  onBeforeUnmount(() => {
    Object.values(audioElements).forEach((audio) => {
      audio?.pause();
      audio?.remove();
    });
  });

  // 删除声音，增加二次确认
  const handleDelete = (item: any) => {
    Modal.confirm({
      title: `确定要删除"${item.name}"吗？`,
      content: '删除后不可恢复',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteVoice(item.id);
          await getVoiceListData(); // 重新获取数据
        } catch (error) {
          message.error('删除失败');
          console.error('删除失败', error);
        }
      },
    });
  };

  // 打开重命名弹窗
  const handleOpenRenameModal = (item: any) => {
    personName.value = item.description || item.name;
    openNameModal.value = true;
    currentRenamingItem.value = item;
  };

  // 确认重命名
  const handleReName = async (item: any) => {
    if (!personName.value) {
      nameInputError.value = true;
      // message.error('请输入新名称')
      return;
    }
    if (!currentRenamingItem.value) {
      return;
    }
    try {
      await renameVoice({ voice_id: item.id, new_name: personName.value, user_id: userId });
      openNameModal.value = false;
      personName.value = '';
      currentRenamingItem.value = null;
      await getVoiceListData();
    } catch (error) {
      message.error('重命名失败');
      console.error('重命名失败', error);
    }
  };

  const handleReGenerate = async (item: any) => {
    try {
      await StartCloning(item.id);
      await getVoiceListData(); // 重新获取数据
    } catch (error) {
      message.error('重新生成失败');
      console.error('重新生成失败', error);
    }
  };

  const handleFailDelete = async (item: any) => {
    try {
      await deleteVoice(item.id);
      await getVoiceListData(); // 重新获取数据
    } catch (error) {
      message.error('删除失败');
      console.error('删除失败', error);
    }
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    title="选择声音"
    :width="1122"
    :mask-closable="false"
    centered
    :style="{ height: '800px', background: '#ffffff', borderRadius: '8px' }"
    :body-style="{ overflow: 'hidden' }"
    :footer="null"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="speakers-modal">
      <div class="speakers-modal-header">
        <div class="speakers-container">
          <div class="speakers-tag">
            <a-tag
              v-for="tag in tagsData"
              :key="tag"
              :class="{ selected: selectedTag === tag }"
              @click="() => handleChange(tag)"
            >
              {{ tag }}
            </a-tag>
          </div>
          <template v-if="selectedLanguage === 'myVoice'">
            <div v-if="!speakersData.myVoice || speakersData.myVoice.length === 0" class="noMyVoice">
              <img :src="EmptyImage" alt="" class="empty-image" />
              <p class="noVoice_text">你还未创建声音，快去创建吧</p>
              <a-button type="primary" ghost class="createVoice_btn" @click="showAddModal = true">
                <Icon class="add-icon" name="jia" size="14" />
                <span>创建声音</span>
              </a-button>
            </div>
            <div v-else class="createSpeaker" @click="showAddModal = true">
              <div class="createSpeaker_text">
                <span style="width: 20px; height: 20px; font-size: 20px; font-weight: 600"> + </span>
                <span>创建声音</span>
              </div>
            </div>
          </template>

          <div class="speakers-container-image">
            <div
              v-for="(item, index) in (speakersData[selectedLanguage] || [])"
              :key="index"
              class="imageBox"
              :class="{
                selected: selectedSpeaker === item.name,
                hovered: hoverStates[index]
              }"
              @click="handleSpeakerSelect(item.name)"
              @mouseenter="
                hoverStates[index] = true;
                myVoiceHoverIndex = index;
              "
              @mouseleave="
                hoverStates[index] = false;
                myVoiceHoverIndex = null;
              "
            >
              <div class="img-container" :class="{ grayscale: hoverStates[index] || isPlaying[index] }">
                <template v-if="selectedLanguage === 'myVoice'">
                  <!-- 失败 -->
                  <template v-if="item.status === 'failed'">
                    <img :src="videoIcon" class="img" />
                    <Loading
                      style="top: -30px; left: -37px; width: 134px; height: 162px"
                      state="声⾳⽣成失败，请重新⽣成"
                      :name="item.name"
                      :handle-re-generate="() => handleReGenerate(item)"
                      :handle-delete="() => handleFailDelete(item)"
                    />
                  </template>

                  <!-- 处理中 -->
                  <template v-else-if="item.status === 'processing'">
                    <img :src="videoIcon" class="img" />
                    <Loading style="top: -30px; left: -37px; width: 134px; height: 162px" state="克隆中" />
                  </template>

                  <template v-else>
                    <img :src="videoIcon" class="img" />
                    <div v-if="hoverStates[index] || isPlaying[index]" class="icon-overlay">
                      <img
                        v-if="!isPlaying[index]"
                        :src="playIcon"
                        alt="播放"
                        @click="playAudio(index, item.sample_minio_url)"
                      />
                      <img v-else :src="stopIcon" alt="停止" @click="playAudio(index, item.sample_minio_url)" />
                    </div>

                    <a-dropdown v-show="myVoiceHoverIndex === index" placement="bottomRight" trigger="hover">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="handleOpenRenameModal(item)">重命名</a-menu-item>
                          <a-menu-item @click="handleDelete(item)">删除</a-menu-item>
                        </a-menu>
                      </template>
                      <Icon
                        v-show="myVoiceHoverIndex === index"
                        name="gengduo"
                        :size="20"
                        class="more-icon"
                        style="position: absolute; right: -25px; top: -15px"
                      />
                    </a-dropdown>
                    <!-- 重命名弹窗 -->
                    <a-popover
                      :open="openNameModal && currentRenamingItem?.id === item.id"
                      trigger="click"
                      placement="topRight"
                      @update:open="
                        (visible: boolean) => {
                          if (!visible) {
                            openNameModal = false;
                            personName = '';
                            currentRenamingItem = null;
                          }
                        }
                      "
                    >
                      <template #content>
                        <div style="width: 220px">
                          <div style="margin-bottom: 8px">重命名</div>
                          <a-input
                            v-model:value="personName"
                            maxlength="10"
                            clear
                            placeholder="请输入新名称，10个字内"
                            style="margin-bottom: 8px"
                            :status="nameInputError ? 'error' : ''"
                            @input="
                              () => {
                                if (personName) nameInputError = false;
                              }
                            "
                          />
                          <div style="text-align: right">
                            <a-button
                              size="small"
                              style="margin-right: 8px"
                              @click="
                                () => {
                                  openNameModal = false;
                                  personName = '';
                                  currentRenamingItem = null;
                                }
                              "
                              >取消</a-button
                            >
                            <a-button size="small" type="primary" @click="handleReName(item)">确定</a-button>
                          </div>
                        </div>
                      </template>
                    </a-popover>
                  </template>
                </template>

                <template v-else>
                  <img class="img" :src="item.image_url" :alt="`${item.name} ${index}`" />
                  <!-- 仅在 hover 状态或音频播放时显示图标图层 -->
                  <div v-if="hoverStates[index] || isPlaying[index]" @click.stop="playAudio(index, item.sample_file_url)" class="icon-overlay">
                    <img
                      v-if="!isPlaying[index]"
                      :src="playIcon"
                      alt="播放"
                    />
                    <img v-else :src="stopIcon" alt="停止" />
                  </div>
                </template>
              </div>
              <!-- <img class="img" :src="item.image_url" :alt="`${item.name} ${index}`" /> -->
              <div class="name">{{ item?.description || item?.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :disabled="!selectedSpeaker" @click="handleOk">确认</a-button>
      </div>
    </template> -->
    <AddModal v-if="showAddModal" v-model:visible="showAddModal"/>
  </a-modal>
</template>

<style lang="less" scoped>
  .speakers-modal {
    border-top: #f0f1f2 solid 1px;

    .speakers-container {
      width: 100%;
      height: 100%;
      background: #ffffff;

      .speakers-tag {
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-start;
        height: 32px;
        margin: 10px 20px;
        // background: #ffffff !important;

        :deep(.ant-tag) {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          padding: 7px 14px;
          background: #ffffff;

          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: right;
          border: none;
          &:hover {
            color: #000000;
          }
          &.selected {
            background: #f2f8ff;
            color: #000000;
          }
        }
      }

      .speakers-container-image {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        max-height: 590px; /* 设置固定高度 */
        overflow-y: auto; /* 启用纵向滚动 */
        overflow-x: hidden; /* 禁用横向滚动 */
        background: #ffffff;
        box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
        }

        -ms-overflow-style: none; /* 适用于 IE 和 Edge */
        scrollbar-width: none; /* 适用于 Firefox */

        .imageBox {
          width: 134px;
          height: 128px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          margin: 10px;
          background: #fafafa;
          /* box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1); */

          .img {
            width: 60px;
            height: 60px;
            /* margin: 2px 0px; */
            /* border-radius: 8px; */
          }

          // &:hover {
          //   border: 1px solid #1777ff;
          // }

          &.hovered {
            border: 2px solid rgba(23, 119, 255, 0.5);
          }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img-container {
            position: relative;
          }

          .grayscale {
            // border-radius: 50%;
          }

          .icon-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(23,24,26,0.5);
          }

          .icon-overlay img {
            width: 16px;
            height: 16px;
          }

          .play-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 60px;
          }
        }
      }

      .noMyVoice {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        width: 100%;
        height: 500px;
        margin-right: 10px;
        text-align: center;

        .noVoice_text {
          width: 182px;
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          margin-top: 8px;
        }

        .createVoice_btn {
          display: flex;
          justify-content: center;
          align-items: center;
          background: #ffffff;
          border-radius: 8px;
          color: #636466;
          border: 1px solid rgba(0, 0, 0, 0.15);

          .add-icon {
            color: #c0c4cc;
            transition: color 0.2s;
            margin-right: 5px;
          }
        }
        .createVoice_btn:hover {
          border: 1px solid #1777ff;
          color: #1777ff;
          :deep(.add-icon) {
            color: #1777ff !important;
          }
        }
      }
    }

    .createSpeaker {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 112px;
      height: 40px;
      background: #1777FF;
      border-radius: 8px;
      margin: 5px 25px 5px 15px;
      border-radius: 4px;
      z-index: 10;

      &:hover {
        border: 1px solid #1777ff;
      }

      :deep(.ant-image-img) {
        width: 36px;
        height: 36px;
        margin: 12px 0 12px 20px;
      }

      .createSpeaker_text {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        background: #1777FF;

        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }

    :deep(.fail) {
      margin-top: 0;
      .fail-text {
        margin: 0px 0px 4px 0px;
      }
    }
  }
</style>




